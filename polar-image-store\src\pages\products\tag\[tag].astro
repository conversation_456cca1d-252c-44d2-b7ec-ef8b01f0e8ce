---
import Layout from '../../../layouts/Layout.astro';
import <PERSON> from '../../../components/Hero.astro';
import ProductCard from '../../../components/ProductCard.astro';
import StructuredData from '../../../components/StructuredData.astro';
import { createPolarClient, transformPolarProduct, getProductsByTag, getTagDisplayName, generateCategoriesWithCounts, generateTagsWithCounts } from '../../../utils/polar';
import type { LocalProduct } from '../../../types/polar';

export async function getStaticPaths() {
  try {
    const polar = createPolarClient();
    const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;

    if (!organizationId) {
      console.warn('POLAR_ORGANIZATION_ID not found');
      return [];
    }

    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const allProducts = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Get unique tags
    const allTags = allProducts
      .flatMap(product => product.tags || [])
      .filter(Boolean)
      .filter((tag, index, array) => array.indexOf(tag) === index);

    return allTags.map((tag) => ({
      params: { tag },
      props: { 
        tag,
        allProducts
      }
    }));
  } catch (error) {
    console.error('Error generating tag paths:', error);
    return [];
  }
}

const { tag, allProducts } = Astro.props as { 
  tag: string; 
  allProducts: LocalProduct[];
};

// Filter products by tag
const products = getProductsByTag(allProducts, tag);
const tagDisplayName = getTagDisplayName(tag);

// Generate categories and tags from all products for navigation
const categories = generateCategoriesWithCounts(allProducts);
const tags = generateTagsWithCounts(allProducts);

// Breadcrumb data
const breadcrumbData = {
  items: [
    { name: "Home", url: import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store' },
    { name: "Products", url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products` },
    { name: `#${tagDisplayName}`, url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products/tag/${tag}` }
  ]
};
---

<Layout
  title={`${tagDisplayName} - Polar Image Store`}
  description={`Browse our collection of ${tagDisplayName.toLowerCase()} digital images and artwork. High-quality digital assets tagged with ${tagDisplayName.toLowerCase()}.`}
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products/tag/${tag}`}
>
  <!-- Breadcrumb Structured Data -->
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />

  <!-- Hero Section -->
  <Hero
    title={`#${tagDisplayName}`}
  >
    <!-- Category Navigation -->
    <Fragment slot="category-navigation">
      {categories.map((category) => (
        <button
          class={`category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap ${
            category.id === 'all'
              ? 'bg-accent-600 text-white shadow-md'
              : 'bg-primary-50 text-primary-700 hover:bg-primary-100 hover:text-primary-900'
          }`}
          data-category={category.id}
        >
          {category.name}
          {category.count && (
            <span class={`text-xs px-2 py-0.5 rounded-full ${
              category.id === 'all'
                ? 'bg-white/20 text-white'
                : 'bg-primary-200 text-primary-600'
            }`}>
              {category.count}
            </span>
          )}
        </button>
      ))}
    </Fragment>

    <!-- Tag Navigation -->
    <Fragment slot="tag-navigation">
      <div class="relative">
        {tags.length > 0 ? (
          <div class="overflow-x-auto scrollbar-hide" id="tagScroll">
            <div class="flex gap-2 pb-2 min-w-max justify-center">
              {tags.slice(0, 10).map((tagItem) => (
                <button
                  class={`tag-tab flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all whitespace-nowrap ${
                    tagItem.id === tag
                      ? 'bg-accent-500 text-white shadow-sm'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900'
                  }`}
                  data-tag={tagItem.id}
                >
                  {tagItem.name}
                  <span class={`text-xs px-1.5 py-0.5 rounded-full ${
                    tagItem.id === tag
                      ? 'bg-white/20 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {tagItem.count}
                  </span>
                </button>
              ))}
            </div>
          </div>
        ) : (
          <div class="text-gray-500 text-sm">
            <p>No tags available yet. Add tags to your products on Polar.sh:</p>
            <code class="bg-gray-100 px-2 py-1 rounded text-xs mt-2 inline-block">
              Key: tags, Value: tag1,tag2,tag3
            </code>
          </div>
        )}
      </div>
    </Fragment>
  </Hero>

  <div class="container mx-auto px-4 py-8">
    <!-- Tag Description -->
    <section class="text-center mb-12">
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        {products.length > 0
          ? `Discover ${products.length} ${products.length === 1 ? 'item' : 'items'} tagged with ${tagDisplayName.toLowerCase()}`
          : `No items found with tag ${tagDisplayName.toLowerCase()}`
        }
      </p>
    </section>


    
    {products.length === 0 ? (
      <div class="text-center py-16">
        <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
          <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold text-gray-900 mb-4">No Items with #{tagDisplayName}</h3>
        <p class="text-gray-600 mb-8">We don't have any items tagged with {tagDisplayName.toLowerCase()} yet.</p>
        <a
          href="/products"
          class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          Browse All Products
        </a>
      </div>
    ) : (
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product) => (
          <ProductCard product={product} />
        ))}
      </div>
    )}
  </div>
</Layout>


