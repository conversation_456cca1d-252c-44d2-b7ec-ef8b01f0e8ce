---
import Layout from '../../../layouts/Layout.astro';
import <PERSON> from '../../../components/Hero.astro';
import ProductCard from '../../../components/ProductCard.astro';
import StructuredData from '../../../components/StructuredData.astro';
import { createPolarClient, transformPolarProduct, getProductsByCategory, getCategoryDisplayName, generateCategoriesWithCounts, generateTagsWithCounts } from '../../../utils/polar';
import type { LocalProduct } from '../../../types/polar';

export async function getStaticPaths() {
  try {
    const polar = createPolarClient();
    const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;

    if (!organizationId) {
      console.warn('POLAR_ORGANIZATION_ID not found');
      return [];
    }

    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const allProducts = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Get unique categories
    const categories = allProducts
      .map(product => product.category)
      .filter((category): category is string => Boolean(category))
      .filter((category, index, array) => array.indexOf(category) === index);

    return categories.map((category) => ({
      params: { category },
      props: { 
        category,
        allProducts
      }
    }));
  } catch (error) {
    console.error('Error generating category paths:', error);
    return [];
  }
}

const { category, allProducts } = Astro.props as { 
  category: string; 
  allProducts: LocalProduct[];
};

// Filter products by category
const products = getProductsByCategory(allProducts, category);
const categoryDisplayName = getCategoryDisplayName(category);

// Generate categories and tags from all products for navigation
const categories = generateCategoriesWithCounts(allProducts);
const tags = generateTagsWithCounts(allProducts);

// Breadcrumb data
const breadcrumbData = {
  items: [
    { name: "Home", url: import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store' },
    { name: "Products", url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products` },
    { name: categoryDisplayName, url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products/category/${category}` }
  ]
};
---

<Layout
  title={`${categoryDisplayName} - Polar Image Store`}
  description={`Browse our collection of ${categoryDisplayName.toLowerCase()} digital images and artwork. High-quality digital assets for creative projects.`}
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products/category/${category}`}
>
  <!-- Breadcrumb Structured Data -->
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />

  <!-- Hero Section -->
  <Hero
    title={categoryDisplayName}
  >
    <!-- Category Navigation -->
    <Fragment slot="category-navigation">
      {categories.map((cat) => (
        <button
          class={`category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap ${
            cat.id === category
              ? 'bg-accent-600 text-white shadow-md'
              : 'bg-primary-50 text-primary-700 hover:bg-primary-100 hover:text-primary-900'
          }`}
          data-category={cat.id}
        >
          {cat.name}
          {cat.count && (
            <span class={`text-xs px-2 py-0.5 rounded-full ${
              cat.id === category
                ? 'bg-white/20 text-white'
                : 'bg-primary-200 text-primary-600'
            }`}>
              {cat.count}
            </span>
          )}
        </button>
      ))}
    </Fragment>

    <!-- Tag Navigation -->
    <Fragment slot="tag-navigation">
      <div class="relative">
        {tags.length > 0 ? (
          <div class="overflow-x-auto scrollbar-hide" id="tagScroll">
            <div class="flex gap-2 pb-2 min-w-max justify-center">
              {tags.slice(0, 10).map((tag) => (
                <button
                  class={`tag-tab flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all whitespace-nowrap ${
                    tag.id === 'all'
                      ? 'bg-accent-500 text-white shadow-sm'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900'
                  }`}
                  data-tag={tag.id}
                >
                  {tag.name}
                  <span class={`text-xs px-1.5 py-0.5 rounded-full ${
                    tag.id === 'all'
                      ? 'bg-white/20 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {tag.count}
                  </span>
                </button>
              ))}
            </div>
          </div>
        ) : (
          <div class="text-gray-500 text-sm">
            <p>No tags available yet. Add tags to your products on Polar.sh:</p>
            <code class="bg-gray-100 px-2 py-1 rounded text-xs mt-2 inline-block">
              Key: tags, Value: tag1,tag2,tag3
            </code>
          </div>
        )}
      </div>
    </Fragment>
  </Hero>

  <div class="container mx-auto px-4 py-8">
    <!-- Category Description -->
    <section class="text-center mb-12">
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        {products.length > 0
          ? `Discover ${products.length} ${categoryDisplayName.toLowerCase()} ${products.length === 1 ? 'item' : 'items'} in our collection`
          : `No ${categoryDisplayName.toLowerCase()} items found`
        }
      </p>
    </section>


    
    {products.length === 0 ? (
      <div class="text-center py-16">
        <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
          <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold text-gray-900 mb-4">No {categoryDisplayName} Found</h3>
        <p class="text-gray-600 mb-8">We don't have any {categoryDisplayName.toLowerCase()} items yet.</p>
        <a
          href="/products"
          class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          Browse All Products
        </a>
      </div>
    ) : (
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.map((product) => (
          <ProductCard product={product} />
        ))}
      </div>
    )}
  </div>
</Layout>


